'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Mail, Loader2 } from 'lucide-react';

export default function TestEmailPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    testInfo?: any;
  } | null>(null);

  const sendTestEmail = async () => {
    if (!email) {
      setResult({
        success: false,
        message: 'Please enter an email address'
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult({
          success: true,
          message: data.message,
          testInfo: data.testInfo
        });
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to send test email'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            📧 Email Deliverability Test
          </h1>
          <p className="text-gray-600">
            Test our new base64 image encoding to improve email deliverability and avoid spam detection.
          </p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Send Test Email
            </CardTitle>
            <CardDescription>
              Enter your email address to receive a test email that compares different image encoding methods.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-1"
              />
              <Button 
                onClick={sendTestEmail} 
                disabled={isLoading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Send Test'
                )}
              </Button>
            </div>

            {result && (
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <div className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className={result.success ? 'text-green-800' : 'text-red-800'}>
                    {result.message}
                  </AlertDescription>
                </div>
                {result.success && result.testInfo && (
                  <div className="mt-3 text-sm text-green-700">
                    <p><strong>Base64 Encoding:</strong> {result.testInfo.base64Working ? '✅ Working' : '❌ Failed'}</p>
                    <p><strong>Sent to:</strong> {result.testInfo.recipient}</p>
                    <p><strong>Time:</strong> {new Date(result.testInfo.sentAt).toLocaleString()}</p>
                  </div>
                )}
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>📋 What to Check After Receiving the Email</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">1. Inbox vs Spam Folder</h3>
                <p className="text-blue-800 text-sm">
                  Check if the email landed in your inbox or spam folder. This is the most important test.
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-900 mb-2">2. Image Display</h3>
                <p className="text-green-800 text-sm">
                  Verify that both logos (base64 and external URL) are displaying correctly.
                </p>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="font-semibold text-yellow-900 mb-2">3. Loading Performance</h3>
                <p className="text-yellow-800 text-sm">
                  Notice if the base64 encoded image loads faster than the external URL image.
                </p>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-semibold text-purple-900 mb-2">4. Multiple Email Clients</h3>
                <p className="text-purple-800 text-sm">
                  Test in different email clients: Gmail, Outlook, Apple Mail, etc.
                </p>
              </div>
            </div>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">🎯 Expected Results</h3>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Email should land in inbox (not spam)</li>
                <li>• Base64 logo should display immediately</li>
                <li>• External URL logo might be blocked by some clients</li>
                <li>• Overall email should look professional and trustworthy</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            This test helps us verify that our email deliverability improvements are working correctly.
          </p>
        </div>
      </div>
    </div>
  );
}

import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Same function as in email-service.ts
function getLogoBase64(): string | null {
  try {
    const logoPath = path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png');
    const logoBuffer = fs.readFileSync(logoPath);
    const base64Logo = logoBuffer.toString('base64');
    return `data:image/png;base64,${base64Logo}`;
  } catch (error) {
    console.warn('Could not load logo file for base64 encoding:', error);
    return null;
  }
}

export async function GET() {
  try {
    const logoBase64 = getLogoBase64();
    
    return NextResponse.json({
      success: !!logoBase64,
      hasBase64: !!logoBase64,
      base64Length: logoBase64?.length || 0,
      preview: logoBase64?.substring(0, 100) || 'No base64 generated',
      workingDirectory: process.cwd(),
      expectedPath: path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png'),
      fileExists: fs.existsSync(path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png'))
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Debug failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST() {
  const logoBase64 = getLogoBase64();
  
  if (!logoBase64) {
    return NextResponse.json({
      error: 'Failed to generate base64 logo'
    }, { status: 500 });
  }

  // Create a minimal test email HTML
  const testEmailHtml = `
    <div style="max-width:600px; margin:0 auto; font-family:Arial, sans-serif; padding:20px;">
      <h2>🧪 Logo Base64 Test</h2>
      
      <div style="background:#f8fafc; padding:20px; border-radius:8px; margin:20px 0;">
        <h3>Base64 Encoded Logo:</h3>
        <img src="${logoBase64}" alt="Base64 Logo" style="max-height:80px; display:block; margin:10px 0;">
        <p style="font-size:12px; color:#666;">
          Base64 length: ${logoBase64.length} characters
        </p>
      </div>

      <div style="background:#fef2f2; padding:20px; border-radius:8px; margin:20px 0;">
        <h3>External URL Logo:</h3>
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="External Logo" style="max-height:80px; display:block; margin:10px 0;">
        <p style="font-size:12px; color:#666;">
          External URL (may be blocked by email clients)
        </p>
      </div>

      <div style="background:#f0f9ff; padding:20px; border-radius:8px; margin:20px 0;">
        <h3>Test Results:</h3>
        <ul style="margin:0; padding-left:20px;">
          <li>Base64 generation: ✅ Success</li>
          <li>Data URL format: ${logoBase64.startsWith('data:image/png;base64,') ? '✅ Valid' : '❌ Invalid'}</li>
          <li>Generated at: ${new Date().toISOString()}</li>
        </ul>
      </div>
    </div>
  `;

  return new Response(testEmailHtml, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
}

import { NextResponse } from 'next/server';
import { getEmailService } from '@/lib/email-service';
import fs from 'fs';
import path from 'path';

// Helper function to get base64 encoded logo for testing
function getLogoBase64(): string | null {
  try {
    const logoPath = path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png');
    const logoBuffer = fs.readFileSync(logoPath);
    const base64Logo = logoBuffer.toString('base64');
    return `data:image/png;base64,${base64Logo}`;
  } catch (error) {
    console.warn('Could not load logo file for base64 encoding:', error);
    return null;
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Email test endpoint. Use POST with { "email": "<EMAIL>" } to send test email.' 
  });
}

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email address is required' },
        { status: 400 }
      );
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      return NextResponse.json(
        { error: 'Valid email is required' },
        { status: 400 }
      );
    }

    const emailService = getEmailService();

    // Get base64 encoded logo
    const logoBase64 = getLogoBase64();
    const logoSrc = logoBase64 || 'https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png';

    // Create test email with both base64 and external image for comparison
    const testEmailHtml = `
      <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
          <h1 style="color: white; margin: 0; font-size: 24px;">📧 Email Deliverability Test</h1>
        </div>

        <!-- Content -->
        <div style="padding:32px;">
          <h2 style="color:#7e22ce; font-size:20px; margin:0 0 20px 0;">Testing Image Encoding Methods</h2>
          
          <p style="color:#4b5563; line-height:1.6; margin:0 0 24px 0;">
            This email tests different image encoding methods to improve deliverability and avoid spam detection.
          </p>

          <!-- Base64 Encoded Logo Test -->
          <div style="background:#f8fafc; padding:20px; border-radius:8px; margin:20px 0; border-left:4px solid #7e22ce;">
            <h3 style="color:#374151; margin:0 0 12px 0;">✅ Base64 Encoded Logo (Recommended)</h3>
            <div style="text-align:center; margin:16px 0;">
              <img src="${logoSrc}" alt="UpZera Logo - Base64" style="max-height:60px; filter: brightness(0) invert(1) hue-rotate(270deg);">
            </div>
            <p style="color:#6b7280; font-size:14px; margin:8px 0 0 0;">
              ${logoBase64 ? '✅ Base64 encoding successful' : '❌ Base64 encoding failed - using fallback URL'}
            </p>
          </div>

          <!-- External URL Logo Test -->
          <div style="background:#fef2f2; padding:20px; border-radius:8px; margin:20px 0; border-left:4px solid #ef4444;">
            <h3 style="color:#374151; margin:0 0 12px 0;">⚠️ External URL Logo (Spam Risk)</h3>
            <div style="text-align:center; margin:16px 0;">
              <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo - External URL" style="max-height:60px; filter: brightness(0) invert(1) hue-rotate(270deg);">
            </div>
            <p style="color:#6b7280; font-size:14px; margin:8px 0 0 0;">
              ⚠️ External URL - may trigger spam filters
            </p>
          </div>

          <!-- Test Results -->
          <div style="background:#f0f9ff; padding:20px; border-radius:8px; margin:20px 0; border-left:4px solid #0ea5e9;">
            <h3 style="color:#374151; margin:0 0 12px 0;">📊 Test Information</h3>
            <ul style="color:#4b5563; margin:0; padding-left:20px;">
              <li>Sent at: ${new Date().toLocaleString()}</li>
              <li>Base64 encoding: ${logoBase64 ? 'Working' : 'Failed'}</li>
              <li>Email client: Check if this lands in inbox or spam</li>
              <li>Image loading: Both images should display properly</li>
            </ul>
          </div>

          <!-- Instructions -->
          <div style="background:#f5f3ff; padding:20px; border-radius:8px; margin:20px 0; border-left:4px solid #7e22ce;">
            <h3 style="color:#7e22ce; margin:0 0 12px 0;">📋 What to Check</h3>
            <ol style="color:#4b5563; margin:0; padding-left:20px; line-height:1.6;">
              <li><strong>Inbox vs Spam:</strong> Did this email land in your inbox or spam folder?</li>
              <li><strong>Image Display:</strong> Are both logos displaying correctly?</li>
              <li><strong>Loading Speed:</strong> Does the base64 image load faster?</li>
              <li><strong>Email Client:</strong> Test in Gmail, Outlook, Apple Mail, etc.</li>
            </ol>
          </div>

          <p style="color:#4b5563; line-height:1.6; margin:24px 0 0 0;">
            Best regards,<br>
            <strong style="color:#7e22ce;">UpZera Development Team</strong>
          </p>
        </div>

        <!-- Footer -->
        <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
          © ${new Date().getFullYear()} UpZera. Email Deliverability Test.
        </div>
      </div>
    `;

    // Send test email
    await emailService.sendEmail({
      to: email,
      subject: '📧 UpZera Email Deliverability Test - Base64 vs External Images',
      html: testEmailHtml,
      attachments: [] // No attachments needed for base64 approach
    });

    return NextResponse.json({
      message: 'Test email sent successfully!',
      success: true,
      testInfo: {
        base64Working: !!logoBase64,
        sentAt: new Date().toISOString(),
        recipient: email
      }
    });

  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      {
        error: 'Failed to send test email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Test if the logo file exists and can be read
    const logoPath = path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png');
    
    console.log('Attempting to read logo from:', logoPath);
    
    // Check if file exists
    if (!fs.existsSync(logoPath)) {
      return NextResponse.json({
        error: 'Logo file not found',
        path: logoPath,
        exists: false
      }, { status: 404 });
    }

    // Read the file
    const logoBuffer = fs.readFileSync(logoPath);
    const base64Logo = logoBuffer.toString('base64');
    const dataUrl = `data:image/png;base64,${base64Logo}`;

    // Get file stats
    const stats = fs.statSync(logoPath);

    return NextResponse.json({
      success: true,
      fileExists: true,
      filePath: logoPath,
      fileSize: stats.size,
      base64Length: base64Logo.length,
      dataUrlLength: dataUrl.length,
      // Include first 100 characters of base64 for verification
      base64Preview: base64Logo.substring(0, 100) + '...',
      // Full data URL for testing
      dataUrl: dataUrl
    });

  } catch (error) {
    console.error('Error reading logo file:', error);
    return NextResponse.json({
      error: 'Failed to read logo file',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    const logoPath = path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png');
    
    if (!fs.existsSync(logoPath)) {
      return NextResponse.json({
        error: 'Logo file not found',
        path: logoPath
      }, { status: 404 });
    }

    const logoBuffer = fs.readFileSync(logoPath);
    const base64Logo = logoBuffer.toString('base64');
    const dataUrl = `data:image/png;base64,${base64Logo}`;

    // Create a simple HTML page to test the base64 image
    const testHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Base64 Image Test</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .test-container { max-width: 600px; margin: 0 auto; }
          .image-test { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
          .success { background-color: #f0f9ff; border-color: #0ea5e9; }
          .error { background-color: #fef2f2; border-color: #ef4444; }
        </style>
      </head>
      <body>
        <div class="test-container">
          <h1>🧪 Base64 Image Encoding Test</h1>
          
          <div class="image-test success">
            <h3>✅ Base64 Encoded Logo</h3>
            <img src="${dataUrl}" alt="Base64 Logo" style="max-height: 100px; display: block; margin: 10px 0;">
            <p><strong>Data URL Length:</strong> ${dataUrl.length} characters</p>
            <p><strong>File Size:</strong> ${logoBuffer.length} bytes</p>
          </div>

          <div class="image-test">
            <h3>🌐 External URL Logo (for comparison)</h3>
            <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="External Logo" style="max-height: 100px; display: block; margin: 10px 0;">
            <p><strong>Source:</strong> External URL</p>
          </div>

          <div class="image-test">
            <h3>📋 Test Results</h3>
            <ul>
              <li><strong>Base64 Generation:</strong> ✅ Successful</li>
              <li><strong>File Read:</strong> ✅ Successful</li>
              <li><strong>Image Display:</strong> ${dataUrl ? '✅ Should work' : '❌ Failed'}</li>
              <li><strong>Data URL Valid:</strong> ${dataUrl.startsWith('data:image/png;base64,') ? '✅ Yes' : '❌ No'}</li>
            </ul>
          </div>

          <div class="image-test">
            <h3>🔍 Debug Information</h3>
            <p><strong>File Path:</strong> ${logoPath}</p>
            <p><strong>Base64 Preview:</strong> ${base64Logo.substring(0, 50)}...</p>
            <p><strong>Generated:</strong> ${new Date().toISOString()}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return new Response(testHtml, {
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('Error in POST test:', error);
    return NextResponse.json({
      error: 'Failed to generate test page',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
